@{
    ViewData["Title"] = "怪物配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>怪物配置管理</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="/">首页</a></li>
                        <li class="breadcrumb-item active">怪物配置管理</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid" id="monsterConfigApp">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">怪物配置列表</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary btn-sm" v-on:click="showCreateModal">
                            <i class="fas fa-plus"></i> 新增怪物配置
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 查询条件 -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <label>怪物编号：</label>
                            <input type="number" class="form-control" v-model="queryForm.monsterNo" placeholder="请输入怪物编号">
                        </div>
                        <div class="col-md-3">
                            <label>怪物名称：</label>
                            <input type="text" class="form-control" v-model="queryForm.name" placeholder="请输入怪物名称">
                        </div>
                        <div class="col-md-3">
                            <label>怪物属性：</label>
                            <select class="form-control" v-model="queryForm.attribute">
                                <option value="">请选择属性</option>
                                <option value="金">金</option>
                                <option value="木">木</option>
                                <option value="水">水</option>
                                <option value="火">火</option>
                                <option value="土">土</option>
                                <option value="神">神</option>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="button" class="btn btn-info mr-2" v-on:click="loadData">
                                <i class="fas fa-search"></i> 查询
                            </button>
                            <button type="button" class="btn btn-secondary" v-on:click="resetQuery">
                                <i class="fas fa-redo"></i> 重置
                            </button>
                        </div>
                    </div>

                    <!-- 数据表格 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>怪物编号</th>
                                    <th>怪物名称</th>
                                    <th>怪物属性</th>
                                    <th>技能编号</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="loading">
                                    <td colspan="6" class="text-center">数据加载中...</td>
                                </tr>
                                <tr v-else-if="!monsterConfigs || monsterConfigs.length === 0">
                                    <td colspan="6" class="text-center">暂无数据</td>
                                </tr>
                                <tr v-else v-for="monsterConfig in monsterConfigs" v-bind:key="monsterConfig.id">
                                    <td>{{ monsterConfig.id }}</td>
                                    <td>{{ monsterConfig.monsterNo }}</td>
                                    <td>{{ monsterConfig.name }}</td>
                                    <td>{{ monsterConfig.attribute }}</td>
                                    <td>{{ monsterConfig.skill || '无' }}</td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-warning me-1" v-on:click="showEditModal(monsterConfig)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" v-on:click="deleteMonsterConfig(monsterConfig.id)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    <div class="row mt-3" v-if="totalCount > 0">
                        <div class="col-sm-12 col-md-5">
                            <div class="dataTables_info">
                                显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <div class="dataTables_paginate paging_simple_numbers">
                                <ul class="pagination">
                                    <li class="paginate_button page-item previous" v-bind:class="{ disabled: currentPage <= 1 }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                    </li>
                                    <li class="paginate_button page-item" v-for="page in visiblePages" v-bind:key="page" v-bind:class="{ active: page === currentPage }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                    </li>
                                    <li class="paginate_button page-item next" v-bind:class="{ disabled: currentPage >= totalPages }">
                                        <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 新增模态框 -->
            <div class="modal fade" id="createModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">新增怪物配置</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form>
                                <div class="form-group">
                                    <label>怪物编号 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" v-model="createForm.monsterNo" placeholder="请输入怪物编号">
                                </div>
                                <div class="form-group">
                                    <label>怪物名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" v-model="createForm.name" placeholder="请输入怪物名称">
                                </div>
                                <div class="form-group">
                                    <label>怪物属性 <span class="text-danger">*</span></label>
                                    <select class="form-control" v-model="createForm.attribute">
                                        <option value="">请选择属性</option>
                                        <option value="金">金</option>
                                        <option value="木">木</option>
                                        <option value="水">水</option>
                                        <option value="火">火</option>
                                        <option value="土">土</option>
                                        <option value="神">神</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>技能编号</label>
                                    <input type="text" class="form-control" v-model="createForm.skill" placeholder="请输入技能编号">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="createMonsterConfig" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑模态框 -->
            <div class="modal fade" id="editModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">编辑怪物配置</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form>
                                <div class="form-group">
                                    <label>怪物编号 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" v-model="editForm.monsterNo" placeholder="请输入怪物编号">
                                </div>
                                <div class="form-group">
                                    <label>怪物名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" v-model="editForm.name" placeholder="请输入怪物名称">
                                </div>
                                <div class="form-group">
                                    <label>怪物属性 <span class="text-danger">*</span></label>
                                    <select class="form-control" v-model="editForm.attribute">
                                        <option value="">请选择属性</option>
                                        <option value="金">金</option>
                                        <option value="木">木</option>
                                        <option value="水">水</option>
                                        <option value="火">火</option>
                                        <option value="土">土</option>
                                        <option value="神">神</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>技能编号</label>
                                    <input type="text" class="form-control" v-model="editForm.skill" placeholder="请输入技能编号">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" v-on:click="updateMonsterConfig" v-bind:disabled="submitting">
                                {{ submitting ? '提交中...' : '确定' }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const { createApp } = Vue;
    
    createApp({
        data() {
            return {
                loading: false,
                submitting: false,
                monsterConfigs: [],
                currentPage: 1,
                pageSize: 10,
                totalCount: 0,
                totalPages: 0,
                queryForm: {
                    monsterNo: null,
                    name: '',
                    attribute: ''
                },
                createForm: {
                    monsterNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                },
                editForm: {
                    id: 0,
                    monsterNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                }
            };
        },
        computed: {
            visiblePages() {
                if (!this.totalPages || this.totalPages <= 0) {
                    return [];
                }
                const start = Math.max(1, this.currentPage - 2);
                const end = Math.min(this.totalPages, this.currentPage + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            }
        },
        mounted() {
            this.loadData();
        },
        methods: {
            // 加载数据
            async loadData() {
                this.loading = true;
                try {
                    const response = await axios.post('/MonsterConfig/GetList', {
                        monsterNo: this.queryForm.monsterNo,
                        name: this.queryForm.name,
                        attribute: this.queryForm.attribute,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });
                    
                    if (response.data.success) {
                        this.monsterConfigs = response.data.data.data || [];
                        this.totalCount = response.data.data.totalCount || 0;
                        this.totalPages = response.data.data.totalPages || 0;
                    } else {
                        alert(response.data.message);
                        this.monsterConfigs = [];
                        this.totalCount = 0;
                        this.totalPages = 0;
                    }
                } catch (error) {
                    console.error('加载数据失败：', error);
                    alert('加载数据失败，请重试');
                    // 确保在错误情况下重置数据
                    this.monsterConfigs = [];
                    this.totalCount = 0;
                    this.totalPages = 0;
                } finally {
                    this.loading = false;
                }
            },
            
            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    monsterNo: null,
                    name: '',
                    attribute: ''
                };
                this.currentPage = 1;
                this.loadData();
            },
            
            // 显示新增模态框
            showCreateModal() {
                this.createForm = {
                    monsterNo: null,
                    name: '',
                    attribute: '',
                    skill: ''
                };
                $('#createModal').modal('show');
            },
            
            // 显示编辑模态框
            showEditModal(item) {
                this.editForm = {
                    id: item.id,
                    monsterNo: item.monsterNo,
                    name: item.name,
                    attribute: item.attribute,
                    skill: item.skill || ''
                };
                $('#editModal').modal('show');
            },
            
            // 创建怪物配置
            async createMonsterConfig() {
                if (!this.validateCreateForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/MonsterConfig/Create', this.createForm);
                    
                    if (response.data.success) {
                        alert('怪物配置创建成功');
                        $('#createModal').modal('hide');
                        this.loadData();
                    } else {
                        alert(response.data.message);
                    }
                } catch (error) {
                    console.error('创建怪物配置失败：', error);
                    alert('创建怪物配置失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },
            
            // 更新怪物配置
            async updateMonsterConfig() {
                if (!this.validateEditForm()) {
                    return;
                }
                
                this.submitting = true;
                try {
                    const response = await axios.post('/MonsterConfig/Update', this.editForm);
                    
                    if (response.data.success) {
                        alert('怪物配置更新成功');
                        $('#editModal').modal('hide');
                        this.loadData();
                    } else {
                        alert(response.data.message);
                    }
                } catch (error) {
                    console.error('更新怪物配置失败：', error);
                    alert('更新怪物配置失败，请重试');
                } finally {
                    this.submitting = false;
                }
            },
            
            // 删除怪物配置
            async deleteMonsterConfig(id) {
                if (!confirm('确定要删除这个怪物配置吗？此操作不可撤销。')) {
                    return;
                }
                
                try {
                    const response = await axios.post('/MonsterConfig/Delete', { id: id });
                    
                    if (response.data.success) {
                        alert('怪物配置删除成功');
                        this.loadData();
                    } else {
                        alert(response.data.message);
                    }
                } catch (error) {
                    console.error('删除怪物配置失败：', error);
                    alert('删除怪物配置失败，请重试');
                }
            },
            
            // 验证创建表单
            validateCreateForm() {
                if (!this.createForm.monsterNo) {
                    alert('请输入怪物编号');
                    return false;
                }
                if (!this.createForm.name) {
                    alert('请输入怪物名称');
                    return false;
                }
                if (!this.createForm.attribute) {
                    alert('请选择怪物属性');
                    return false;
                }
                return true;
            },
            
            // 验证编辑表单
            validateEditForm() {
                if (!this.editForm.monsterNo) {
                    alert('请输入怪物编号');
                    return false;
                }
                if (!this.editForm.name) {
                    alert('请输入怪物名称');
                    return false;
                }
                if (!this.editForm.attribute) {
                    alert('请选择怪物属性');
                    return false;
                }
                return true;
            },
            
            // 切换页码
            changePage(page) {
                if (!this.totalPages || page < 1 || page > this.totalPages || page === this.currentPage) {
                    return;
                }
                this.currentPage = page;
                this.loadData();
            }
        }
    }).mount('#monsterConfigApp');
});
</script> 