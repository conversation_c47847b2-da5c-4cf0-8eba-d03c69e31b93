@{
    ViewData["Title"] = "道具配置管理";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<style>
    /* 科幻脚本内容样式 */
    .cyber-script-content {
        position: relative;
        cursor: pointer;
        max-width: 200px;
        overflow: hidden;
    }

    .cyber-script-preview {
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: 'Courier New', monospace;
        font-size: 12px;
        color: var(--cyber-blue);
        background: rgba(0, 212, 255, 0.1);
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid rgba(0, 212, 255, 0.3);
        transition: var(--transition);
    }

    .cyber-script-preview:hover {
        background: rgba(0, 212, 255, 0.2);
        border-color: var(--cyber-blue);
    }

    /* 科幻气泡框样式 */
    .cyber-script-tooltip {
        position: absolute;
        top: 100%;
        left: 0;
        z-index: 1000;
        display: none;
        min-width: 300px;
        max-width: 500px;
        background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 12px;
        box-shadow: var(--card-glow), var(--neon-glow);
        padding: 0;
        margin-top: 5px;
        font-size: 12px;
    }

    .cyber-script-content:hover .cyber-script-tooltip {
        display: block;
    }

    .cyber-tooltip-header {
        background: linear-gradient(135deg, var(--dark-card), var(--dark-surface));
        padding: 8px 12px;
        border-bottom: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 12px 12px 0 0;
        font-weight: bold;
        color: var(--cyber-blue);
    }

    .cyber-tooltip-content {
        padding: 12px;
        max-height: 200px;
        overflow-y: auto;
    }

    .cyber-tooltip-content pre {
        margin: 0;
        font-family: 'Courier New', monospace;
        font-size: 11px;
        line-height: 1.4;
        color: var(--text-primary);
        background: var(--dark-surface);
        padding: 8px;
        border-radius: 4px;
        white-space: pre-wrap;
        word-wrap: break-word;
    }

    .cyber-tooltip-footer {
        padding: 8px 12px;
        border-top: 1px solid rgba(0, 212, 255, 0.3);
        background: var(--dark-surface);
        border-radius: 0 0 12px 12px;
    }

    /* 科幻箭头 */
    .cyber-script-tooltip::before {
        content: '';
        position: absolute;
        top: -5px;
        left: 20px;
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid rgba(0, 212, 255, 0.3);
    }

    .cyber-script-tooltip::after {
        content: '';
        position: absolute;
        top: -4px;
        left: 20px;
        width: 0;
        height: 0;
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid var(--dark-card);
    }

    /* 科幻表格列宽度调整 */
    .cyber-table th:nth-child(10), .cyber-table td:nth-child(10) { max-width: 220px; min-width: 200px; }
    .cyber-table th:nth-child(11), .cyber-table td:nth-child(11) { width: 80px; }
    .cyber-table th:nth-child(12), .cyber-table td:nth-child(12) { width: 120px; }
</style>

<div id="app">
    <!-- 主控制台 -->
    <div class="cyber-card">
        <div class="cyber-card-header">
            <div class="cyber-icon">
                <i class="fas fa-cubes"></i>
            </div>
            <h5 class="cyber-card-title">道具配置控制台</h5>
            <div class="ms-auto">
                <button type="button" class="cyber-btn cyber-btn-success me-2" v-on:click="batchEnable" v-bind:disabled="selectedItems.length === 0">
                                    <i class="fas fa-check"></i> 批量启用
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-warning me-2" v-on:click="batchDisable" v-bind:disabled="selectedItems.length === 0">
                                    <i class="fas fa-ban"></i> 批量禁用
                                </button>
                                <button type="button" class="cyber-btn" v-on:click="showCreateModal">
                                    <i class="fas fa-plus"></i> 新增道具
                                </button>
            </div>
        </div>

        <div class="container-fluid">
            <!-- 查询控制面板 -->
            <div class="cyber-card">
                <div class="cyber-card-header">
                    <div class="cyber-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h5 class="cyber-card-title">查询控制面板</h5>
                </div>
                <div>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">道具编号</label>
                                <input type="number" class="cyber-form-control" v-model="queryForm.ItemNo" placeholder="道具编号">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">道具名称</label>
                                <input type="text" class="cyber-form-control" v-model="queryForm.Name" placeholder="道具名称">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">道具类型</label>
                                <div class="cyber-select-wrapper">
                                    <select class="cyber-form-control" v-model="queryForm.Type">
                                        <option value="">全部类型</option>
                                        <option v-for="type in types" :key="type" :value="type" v-text="type"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">道具品质</label>
                                <div class="cyber-select-wrapper">
                                    <select class="cyber-form-control" v-model="queryForm.Quality">
                                        <option value="">全部品质</option>
                                        <option v-for="quality in qualities" :key="quality" :value="quality" v-text="quality"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">脚本内容</label>
                                <input type="text" class="cyber-form-control" v-model="queryForm.ScriptContent" placeholder="搜索脚本内容">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="cyber-form-group">
                                <label class="cyber-form-label">启用状态</label>
                                <div class="cyber-select-wrapper">
                                    <select class="cyber-form-control" v-model="queryForm.IsActive">
                                        <option value="">全部状态</option>
                                        <option value="true">启用</option>
                                        <option value="false">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="cyber-form-group">
                                <button type="button" class="cyber-btn me-2" v-on:click="searchItems">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <button type="button" class="cyber-btn cyber-btn-outline" v-on:click="resetSearch">
                                    <i class="fas fa-refresh"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 道具数据表 -->
            <div class="cyber-card">
                <div class="cyber-card-header">
                    <div class="cyber-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <h5 class="cyber-card-title">道具数据表</h5>
                </div>
                <div class="cyber-table-container">
                    <table class="cyber-table">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" v-model="selectAllComputed" class="form-check-input"></th>
                                            <th>ID</th>
                                            <th>道具编号</th>
                                            <th>道具名称</th>
                                            <th>道具类型</th>
                                            <th>道具品质</th>
                                            <th>道具价格</th>
                                            <th>道具图标</th>
                                            <th>使用限制</th>
                                            <th>道具描述</th>
                                            <th>启用状态</th>
                                            <th>脚本内容</th>
                                            <th>脚本状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-if="loading">
                                            <td colspan="14" class="text-center">数据加载中...</td>
                                        </tr>
                                        <tr v-else-if="items.length === 0">
                                            <td colspan="14" class="text-center">暂无数据</td>
                                        </tr>
                                        <tr v-else v-for="item in items" :key="item.id">
                                            <td><input type="checkbox" v-model="selectedItems" :value="item.id" class="form-check-input"></td>
                                            <td v-text="item.id"></td>
                                            <td v-text="item.itemNo"></td>
                                            <td v-text="item.name"></td>
                                            <td v-text="item.type || '-'"></td>
                                            <td v-text="item.quality || '-'"></td>
                                            <td v-text="item.price || 0"></td>
                                            <td v-text="item.icon || '-'"></td>
                                            <td v-text="item.useLimit || '-'"></td>
                                            <td v-text="item.description || '-'"></td>
                                            <td>
                                                <span v-if="item.isActive" class="badge badge-success">启用</span>
                                                <span v-else class="badge badge-danger">禁用</span>
                                            </td>
                                            <td>
                                                <div v-if="item.script && item.script.script" class="cyber-script-content" :title="item.script.script">
                                                    <span class="cyber-script-preview" v-text="getScriptPreview(item.script.script)"></span>
                                                    <div class="script-tooltip">
                                                        <div class="tooltip-header"><strong>脚本内容预览</strong></div>
                                                        <div class="tooltip-content"><pre v-text="item.script.script"></pre></div>
                                                        <div class="tooltip-footer" v-if="item.script.description">
                                                            <small class="text-muted">说明：<span v-text="item.script.description"></span></small>
                                                        </div>
                                                    </div>
                                                </div>
                                                <span v-else class="text-muted">-</span>
                                            </td>
                                            <td>
                                                <span v-if="item.script" class="badge badge-success">有脚本</span>
                                                <span v-else class="badge badge-secondary">无脚本</span>
                                            </td>
                                            <td>
                                                <button type="button" class="cyber-btn cyber-btn-info cyber-btn-sm" v-on:click="viewDetails(item)" title="查看详情">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-warning cyber-btn-sm" v-on:click="showEditModal(item)" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-outline cyber-btn-sm" v-on:click="showScriptModal(item)" title="编辑脚本">
                                                    <i class="fas fa-code"></i>
                                                </button>
                                                <button type="button" class="cyber-btn cyber-btn-danger cyber-btn-sm" v-on:click="deleteItem(item.id)" title="删除">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                                <nav v-if="totalCount > 0">
                                    <ul class="cyber-pagination justify-content-center">
                                        <li class="page-item" :class="{ disabled: currentPage <= 1 }">
                                            <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>
                                        <li class="page-item" v-for="page in visiblePages" :key="page" :class="{ active: page === currentPage }">
                                            <a class="page-link" href="#" v-on:click.prevent="changePage(page)" v-text="page"></a>
                                        </li>
                                        <li class="page-item" :class="{ disabled: currentPage >= totalPages }">
                                            <a class="page-link" href="#" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                    <div class="text-center mt-2">
                                        共 <span v-text="totalCount"></span> 条记录，第 <span v-text="currentPage"></span> / <span v-text="totalPages"></span> 页
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 新增/编辑道具模态框 -->
    <div class="modal fade" id="itemModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" v-text="modalTitle"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form v-no:submit.prevent="saveItem">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">道具编号 <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" v-model="itemForm.itemNo" placeholder="请输入道具编号" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">道具名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" v-model="itemForm.name" placeholder="请输入道具名称" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">道具类型</label>
                                    <input type="text" class="form-control" v-model="itemForm.type" placeholder="请输入道具类型">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">道具品质</label>
                                    <input type="text" class="form-control" v-model="itemForm.quality" placeholder="请输入道具品质">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">道具价格</label>
                                    <input type="number" class="form-control" v-model="itemForm.price" placeholder="请输入道具价格">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">道具图标</label>
                                    <input type="text" class="form-control" v-model="itemForm.icon" placeholder="请输入道具图标路径">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">使用限制</label>
                            <input type="text" class="form-control" v-model="itemForm.useLimit" placeholder="请输入使用限制">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">道具描述</label>
                            <textarea class="form-control" rows="3" v-model="itemForm.description" placeholder="请输入道具描述"></textarea>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" v-model="itemForm.isActive" id="isActive">
                                <label class="form-check-label" for="isActive">
                                    启用道具
                                </label>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">扩展信息（JSON格式）</label>
                            <textarea class="form-control" rows="4" v-model="itemForm.extra" placeholder="请输入JSON格式的扩展信息"></textarea>
                            <small class="form-text text-muted">支持JSON格式的扩展属性信息</small>
                        </div>
                        
                        <!-- 脚本信息 -->
                        <hr>
                        <h6>脚本信息</h6>
                        <div class="mb-3">
                            <label class="form-label">脚本说明</label>
                            <input type="text" class="form-control" v-model="scriptForm.description" placeholder="请输入脚本说明">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">脚本内容</label>
                            <textarea class="form-control" rows="10" v-model="scriptForm.script" placeholder="请输入脚本内容"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="saveItemBtn">
                        <span id="saveSpinner" class="spinner-border spinner-border-sm me-2" style="display: none;"></span>
                        <span id="saveText">保存</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">道具详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row" v-if="currentItem">
                        <div class="col-md-6 mb-2">
                            <strong>道具编号:</strong> <span v-text="currentItem.itemNo"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>道具名称:</strong> <span v-text="currentItem.name"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>道具类型:</strong> <span v-text="currentItem.type || '-'"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>道具品质:</strong> <span v-text="currentItem.quality || '-'"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>道具价格:</strong> <span v-text="currentItem.price || 0"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>道具图标:</strong> <span v-text="currentItem.icon || '-'"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>使用限制:</strong> <span v-text="currentItem.useLimit || '-'"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>创建时间:</strong> <span v-text="formatDate(currentItem.createTime)"></span>
                        </div>
                        <div class="col-md-6 mb-2">
                            <strong>启用状态:</strong> 
                            <span v-if="currentItem.isActive" class="badge bg-success">启用</span>
                            <span v-else class="badge bg-danger">禁用</span>
                        </div>
                        <div class="col-12 mb-2">
                            <strong>道具描述:</strong> <span v-text="currentItem.description || '-'"></span>
                        </div>
                        <div class="col-12 mb-2" v-if="currentItem.extra">
                            <strong>扩展信息:</strong>
                            <pre class="bg-light p-2 rounded mt-1" style="max-height: 100px; overflow-y: auto;" v-text="JSON.stringify(currentItem.extra, null, 2)"></pre>
                        </div>
                        
                        <!-- 脚本信息 -->
                        <div class="col-12 mt-3">
                            <h6>脚本信息</h6>
                            <div v-if="currentItem.script" class="border p-3 rounded">
                                <div class="mb-2">
                                    <strong>脚本说明:</strong> <span v-text="currentItem.script.description || '-'"></span>
                                </div>
                                <div class="mb-2">
                                    <strong>脚本内容:</strong>
                                    <pre class="bg-light p-2 rounded mt-1" style="max-height: 200px; overflow-y: auto;" v-text="currentItem.script.script || '-'"></pre>
                                </div>
                                <div class="mb-2">
                                    <strong>脚本创建时间:</strong> <span v-text="formatDate(currentItem.script.createTime)"></span>
                                </div>
                            </div>
                            <div v-else class="text-muted">
                                该道具暂无脚本信息
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本编辑模态框 -->
    <div class="modal fade" id="scriptModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">道具脚本编辑</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div v-if="currentItem">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            正在编辑道具：<strong v-text="currentItem.name"></strong>（编号：<span v-text="currentItem.itemNo"></span>）
                        </div>
                    <form v-on:submit.prevent="saveScript">
                            <div class="mb-3">
                                <label class="form-label">脚本说明</label>
                                <input type="text" class="form-control" v-model="scriptForm.description" placeholder="请输入脚本说明">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">脚本内容</label>
                                <textarea class="form-control" rows="15" v-model="scriptForm.script" placeholder="请输入脚本内容"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" v-on:click="saveScript" :disabled="saving">
                        <span v-if="saving" class="spinner-border spinner-border-sm me-2"></span>
                        <span v-text="saving ? '保存中...' : '保存脚本'"></span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入必要的脚本 -->
<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const { createApp } = Vue;

        const app = createApp({
        data() {
            return {
                loading: false,
                saving: false,
                items: [],
                types: @Html.Raw(Json.Serialize(ViewBag.Types ?? new List<string>())),
                qualities: @Html.Raw(Json.Serialize(ViewBag.Qualities ?? new List<string>())),
                totalCount: 0,
                currentPage: 1,
                pageSize: 10,
                queryForm: {
                    ItemNo: null,
                    Name: '',
                    Type: '',
                    Quality: '',
                    ScriptContent: '',
                    IsActive: '',
                    Page: 1,
                    PageSize: 10
                },
                itemForm: {
                    id: 0,
                    itemNo: null,
                    name: '',
                    type: '',
                    quality: '',
                    price: null,
                    icon: '',
                    useLimit: '',
                    description: '',
                    extra: '',
                    isActive: true
                },
                scriptForm: {
                    id: null,
                    itemNo: null,
                    script: '',
                    description: ''
                },
                currentItem: null,
                isEdit: false,
                modalTitle: '新增道具',
                selectedItems: [],
                selectAll: false,
                isSaving: false // 防重复提交标志
            };
        },
        computed: {
            totalPages() {
                return Math.ceil(this.totalCount / this.pageSize);
            },
            visiblePages() {
                const start = Math.max(1, this.currentPage - 2);
                const end = Math.min(this.totalPages, this.currentPage + 2);
                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            },
            // 监听选中项变化，更新全选状态
            selectAllComputed: {
                get() {
                    return this.items.length > 0 && this.selectedItems.length === this.items.length;
                },
                set(value) {
                    this.selectAll = value;
                    this.toggleSelectAll();
                }
            }
        },
        mounted() {
            this.loadItems();

            // 监听模态框关闭事件
            $('#itemModal').on('hidden.bs.modal', () => {
                console.log('模态框关闭，完全清空表单数据');
                this.resetFormDataCompletely();
            });
        },
        methods: {


            forceUpdate() {
                // 强制Vue重新渲染
                this.$forceUpdate();
            },
            forceBindFormData() {
                // 强制绑定表单数据到DOM元素（备用方案）
                try {
                    const modal = document.getElementById('itemModal');
                    if (!modal) return;

                    // 先清空表单，再绑定新数据
                    this.clearFormData();

                    // 绑定道具编号
                    const itemNoInput = modal.querySelector('input[type="number"]');
                    if (itemNoInput && this.itemForm.itemNo !== null) {
                        itemNoInput.value = this.itemForm.itemNo;
                    }

                    // 绑定道具名称
                    const nameInput = modal.querySelector('input[placeholder="请输入道具名称"]');
                    if (nameInput && this.itemForm.name) {
                        nameInput.value = this.itemForm.name;
                    }

                    // 绑定道具类型
                    const typeInput = modal.querySelector('input[placeholder="请输入道具类型"]');
                    if (typeInput && this.itemForm.type) {
                        typeInput.value = this.itemForm.type;
                    }

                    // 绑定道具品质
                    const qualityInput = modal.querySelector('input[placeholder="请输入道具品质"]');
                    if (qualityInput && this.itemForm.quality) {
                        qualityInput.value = this.itemForm.quality;
                    }

                    // 绑定道具价格
                    const priceInput = modal.querySelector('input[placeholder="请输入道具价格"]');
                    if (priceInput && this.itemForm.price !== null) {
                        priceInput.value = this.itemForm.price;
                    }

                    // 绑定启用状态
                    const isActiveCheckbox = modal.querySelector('#isActive');
                    if (isActiveCheckbox) {
                        isActiveCheckbox.checked = this.itemForm.isActive;
                    }

                    // 绑定道具描述
                    const descriptionTextarea = modal.querySelector('textarea[placeholder="请输入道具描述"]');
                    if (descriptionTextarea && this.itemForm.description) {
                        descriptionTextarea.value = this.itemForm.description;
                    }

                    // 绑定脚本内容
                    const scriptTextarea = modal.querySelector('textarea[placeholder="请输入脚本内容"]');
                    if (scriptTextarea && this.scriptForm.script) {
                        scriptTextarea.value = this.scriptForm.script;
                    }

                    // 绑定脚本说明
                    const scriptDescInput = modal.querySelector('input[placeholder="请输入脚本说明"]');
                    if (scriptDescInput && this.scriptForm.description) {
                        scriptDescInput.value = this.scriptForm.description;
                    }

                } catch (error) {
                    console.error('强制绑定表单数据时出错:', error);
                }
            },
            collectFormData() {
                // 从DOM表单中收集用户输入的数据
                try {
                    const modal = document.getElementById('itemModal');
                    if (!modal) return;

                    // 收集道具编号
                    const itemNoInput = modal.querySelector('input[type="number"]');
                    if (itemNoInput) {
                        this.itemForm.itemNo = itemNoInput.value ? parseInt(itemNoInput.value) : null;
                    }

                    // 收集道具名称
                    const nameInput = modal.querySelector('input[placeholder="请输入道具名称"]');
                    if (nameInput) {
                        this.itemForm.name = nameInput.value || '';
                    }

                    // 收集道具类型
                    const typeInput = modal.querySelector('input[placeholder="请输入道具类型"]');
                    if (typeInput) {
                        this.itemForm.type = typeInput.value || '';
                    }

                    // 收集道具品质
                    const qualityInput = modal.querySelector('input[placeholder="请输入道具品质"]');
                    if (qualityInput) {
                        this.itemForm.quality = qualityInput.value || '';
                    }

                    // 收集道具价格
                    const priceInput = modal.querySelector('input[placeholder="请输入道具价格"]');
                    if (priceInput) {
                        this.itemForm.price = priceInput.value ? parseInt(priceInput.value) : null;
                    }

                    // 收集道具图标
                    const iconInput = modal.querySelector('input[placeholder="请输入道具图标路径"]');
                    if (iconInput) {
                        this.itemForm.icon = iconInput.value || '';
                    }

                    // 收集使用限制
                    const useLimitInput = modal.querySelector('input[placeholder="请输入使用限制"]');
                    if (useLimitInput) {
                        this.itemForm.useLimit = useLimitInput.value || '';
                    }

                    // 收集道具描述
                    const descriptionTextarea = modal.querySelector('textarea[placeholder="请输入道具描述"]');
                    if (descriptionTextarea) {
                        this.itemForm.description = descriptionTextarea.value || '';
                    }

                    // 收集启用状态
                    const isActiveCheckbox = modal.querySelector('#isActive');
                    if (isActiveCheckbox) {
                        this.itemForm.isActive = isActiveCheckbox.checked;
                    }

                    // 收集扩展信息
                    const extraTextarea = modal.querySelector('textarea[placeholder="请输入JSON格式的扩展信息"]');
                    if (extraTextarea) {
                        this.itemForm.extra = extraTextarea.value || '';
                    }

                    // 收集脚本说明
                    const scriptDescInput = modal.querySelector('input[placeholder="请输入脚本说明"]');
                    if (scriptDescInput) {
                        this.scriptForm.description = scriptDescInput.value || '';
                    }

                    // 收集脚本内容
                    const scriptTextarea = modal.querySelector('textarea[placeholder="请输入脚本内容"]');
                    if (scriptTextarea) {
                        this.scriptForm.script = scriptTextarea.value || '';
                    }

                    console.log('收集到的表单数据:', {
                        itemForm: this.itemForm,
                        scriptForm: this.scriptForm
                    });

                } catch (error) {
                    console.error('收集表单数据时出错:', error);
                }
            },
            clearFormData() {
                // 清空DOM表单中的所有数据
                try {
                    const modal = document.getElementById('itemModal');
                    if (!modal) return;

                    // 清空所有输入框
                    const inputs = modal.querySelectorAll('input[type="text"], input[type="number"]');
                    inputs.forEach(input => {
                        input.value = '';
                    });

                    // 清空所有文本域
                    const textareas = modal.querySelectorAll('textarea');
                    textareas.forEach(textarea => {
                        textarea.value = '';
                    });

                    // 重置复选框
                    const checkboxes = modal.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });

                    console.log('DOM表单数据已清空');

                } catch (error) {
                    console.error('清空表单数据时出错:', error);
                }
            },

            updateModalTitle() {
                // 直接更新DOM元素
                const titleElement = document.querySelector('#itemModal .modal-title');
                if (titleElement) {
                    titleElement.textContent = this.modalTitle;
                }
            },
            updateAllModalContent() {
                // 更新所有可能的Vue模板语法
                this.$nextTick(() => {
                    // 更新标题
                    this.updateModalTitle();

                    // 更新保存按钮文本
                    const saveButton = document.querySelector('#itemModal .btn-primary');
                    if (saveButton && this.saving) {
                        saveButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
                    } else if (saveButton) {
                        saveButton.innerHTML = '保存';
                    }


                });
            },
            async loadItems() {
                this.loading = true;
                try {
                    const queryParams = {
                        ...this.queryForm,
                        Page: this.currentPage,
                        PageSize: this.pageSize
                    };

                    if (queryParams.IsActive === '') {
                        queryParams.IsActive = null;
                    }

                    const response = await axios.post('/ItemConfig/GetListWithScript', queryParams);

                    if (response.data.success) {
                        this.items = response.data.data || [];
                        this.totalCount = response.data.total || 0;
                        this.selectedItems = [];
                        this.selectAll = false;
                    } else {
                        this.showMessage('加载失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('加载失败：' + error.message, 'error');
                } finally {
                    this.loading = false;
                }
            },
            searchItems() {
                this.currentPage = 1;
                this.loadItems();
            },
            resetSearch() {
                this.queryForm = {
                    ItemNo: null,
                    Name: '',
                    Type: '',
                    Quality: '',
                    ScriptContent: '',
                    IsActive: '',
                    Page: 1,
                    PageSize: 10
                };
                this.currentPage = 1;
                this.loadItems();
            },
            changePage(page) {
                if (page >= 1 && page <= this.totalPages) {
                    this.currentPage = page;
                    this.loadItems();
                }
            },
            async showCreateModal() {
                this.isEdit = false;
                this.modalTitle = '新增道具';
                this.itemForm = {
                    id: 0,
                    itemNo: null,
                    name: '',
                    type: '',
                    quality: '',
                    price: null,
                    icon: '',
                    useLimit: '',
                    description: '',
                    extra: '',
                    isActive: true
                };
                this.scriptForm = {
                    id: null,
                    itemNo: null,
                    script: '',
                    description: ''
                };

                this.$nextTick(() => {
                    $('#itemModal').modal('show');

                    // 重置保存按钮状态并绑定事件
                    setTimeout(() => {
                        const saveBtn = document.getElementById('saveItemBtn');
                        const saveSpinner = document.getElementById('saveSpinner');
                        const saveText = document.getElementById('saveText');

                        if (saveBtn) {
                            saveBtn.disabled = false;
                            // 移除所有旧的事件监听器
                            const newBtn = saveBtn.cloneNode(true);
                            saveBtn.parentNode.replaceChild(newBtn, saveBtn);

                            // 添加新的事件监听器
                            const self = this; // 保存Vue实例的引用
                            newBtn.addEventListener('click', function() {
                                console.log('按钮点击事件触发');
                                self.saveItem();
                            });
                        }
                        if (saveSpinner) saveSpinner.style.display = 'none';
                        if (saveText) saveText.textContent = '保存';
                    }, 100);
                });
            },
            async showEditModal(item) {
                console.log('showEditModal 开始，设置isEdit为true');
                this.isEdit = true;
                this.modalTitle = '编辑道具';
                this.saving = false;

                // 先清空表单数据（但保持编辑状态）
                this.resetFormData();

                console.log('清空表单后的isEdit状态:', this.isEdit);

                // 重置保存按钮状态并绑定事件
                setTimeout(() => {
                    const saveBtn = document.getElementById('saveItemBtn');
                    const saveSpinner = document.getElementById('saveSpinner');
                    const saveText = document.getElementById('saveText');

                    if (saveBtn) {
                        saveBtn.disabled = false;
                        // 移除所有旧的事件监听器
                        const newBtn = saveBtn.cloneNode(true);
                        saveBtn.parentNode.replaceChild(newBtn, saveBtn);

                        // 添加新的事件监听器
                        const self = this; // 保存Vue实例的引用
                        newBtn.addEventListener('click', function() {
                            console.log('按钮点击事件触发');
                            self.saveItem();
                        });
                    }
                    if (saveSpinner) saveSpinner.style.display = 'none';
                    if (saveText) saveText.textContent = '保存';
                }, 100);

                try {
                    console.log('开始加载编辑数据，item.id:', item.id);

                    // 先清空DOM表单
                    this.clearFormData();

                    const response = await axios.get(`/ItemConfig/GetByIdWithScript?id=${item.id}`);
                    console.log('API响应:', response.data);

                    if (response.data.success) {
                        const data = response.data.data;
                        console.log('获取到的数据:', data);

                        // 兼容不同的属性命名方式（驼峰和帕斯卡）
                        // 使用Object.assign来确保响应式更新
                        Object.assign(this.itemForm, {
                            id: data.id || data.Id || 0,
                            itemNo: data.itemNo || data.ItemNo || null,
                            name: data.name || data.Name || '',
                            type: data.type || data.Type || '',
                            quality: data.quality || data.Quality || '',
                            price: data.price || data.Price || null,
                            icon: data.icon || data.Icon || '',
                            useLimit: data.useLimit || data.UseLimit || '',
                            description: data.description || data.Description || '',
                            extra: data.extra || data.Extra ? JSON.stringify(data.extra || data.Extra, null, 2) : '',
                            isActive: data.isActive !== undefined ? data.isActive : (data.IsActive !== undefined ? data.IsActive : true)
                        });

                        console.log('设置的itemForm:', this.itemForm);

                        // 处理脚本数据
                        const scriptData = data.script || data.Script;
                        Object.assign(this.scriptForm, {
                            id: scriptData?.id || scriptData?.Id || null,
                            itemNo: data.itemNo || data.ItemNo || null,
                            script: scriptData?.script || scriptData?.Script || '',
                            description: scriptData?.description || scriptData?.Description || ''
                        });

                        console.log('设置的scriptForm:', this.scriptForm);

                        // 强制更新Vue组件
                        this.$forceUpdate();

                        // 等待Vue更新完成后再显示模态框
                        this.$nextTick(() => {
                            // 再次强制更新以确保数据绑定
                            this.$forceUpdate();

                            // 延迟显示模态框，确保数据已经绑定
                            setTimeout(() => {
                                $('#itemModal').modal('show');

                                // 模态框显示后强制绑定数据
                                setTimeout(() => {
                                    this.forceBindFormData();
                                    this.$forceUpdate();
                                }, 50);
                            }, 100);
                        });

                    } else {
                        console.error('API返回失败:', response.data.message);
                        this.showMessage('加载失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    console.error('请求失败:', error);
                    this.showMessage('加载失败：' + error.message, 'error');
                }
            },
            async viewDetails(item) {
                try {
                    const response = await axios.get(`/ItemConfig/GetByIdWithScript?id=${item.id}`);

                    if (response.data.success) {
                        this.currentItem = response.data.data;
                        this.$nextTick(() => {
                            $('#detailModal').modal('show');
                        });
                    } else {
                        this.showMessage('加载失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('加载失败：' + error.message, 'error');
                }
            },
            showScriptModal(item) {
                this.currentItem = item;
                this.scriptForm = {
                    id: item.script ? item.script.id : null,
                    itemNo: item.itemNo,
                    script: item.script ? item.script.script : '',
                    description: item.script ? item.script.description : ''
                };
                this.$nextTick(() => {
                    $('#scriptModal').modal('show');
                });
            },
            async saveItem() {
                console.log('saveItem 开始执行');

                // 防重复提交
                if (this.isSaving) {
                    console.log('正在保存中，忽略重复点击');
                    return;
                }

                // 直接控制按钮状态
                const saveBtn = document.getElementById('saveItemBtn');
                const saveSpinner = document.getElementById('saveSpinner');
                const saveText = document.getElementById('saveText');

                // 从DOM表单中收集实际的用户输入值
                this.collectFormData();

                if (!this.itemForm.itemNo || !this.itemForm.name) {
                    this.showMessage('请填写必填项', 'warning');
                    return;
                }

                // 验证扩展信息JSON格式
                if (this.itemForm.extra && this.itemForm.extra.trim()) {
                    try {
                        JSON.parse(this.itemForm.extra);
                    } catch (error) {
                        this.showMessage('扩展信息JSON格式错误，请检查格式', 'error');
                        return;
                    }
                }

                // 准备保存的数据
                const saveData = {
                    config: {
                        id: this.itemForm.id,
                        itemNo: this.itemForm.itemNo,
                        name: this.itemForm.name,
                        type: this.itemForm.type,
                        quality: this.itemForm.quality,
                        price: this.itemForm.price,
                        icon: this.itemForm.icon,
                        useLimit: this.itemForm.useLimit,
                        description: this.itemForm.description,
                        isActive: this.itemForm.isActive,
                        // 确保extra字段正确处理
                        extra: this.itemForm.extra && this.itemForm.extra.trim()
                            ? JSON.parse(this.itemForm.extra)
                            : null
                    },
                    script: {
                        id: this.scriptForm.id,
                        itemNo: this.scriptForm.itemNo,
                        script: this.scriptForm.script,
                        description: this.scriptForm.description
                    }
                };

                console.log('准备保存的数据:', saveData);
                console.log('itemForm原始数据:', this.itemForm);
                console.log('scriptForm原始数据:', this.scriptForm);

                // 设置保存状态
                this.isSaving = true;

                // 设置按钮为加载状态
                if (saveBtn) saveBtn.disabled = true;
                if (saveSpinner) saveSpinner.style.display = 'inline-block';
                if (saveText) saveText.textContent = '保存中...';

                try {
                    console.log('保存时的isEdit状态:', this.isEdit);
                    console.log('保存时的itemForm.id:', this.itemForm.id);
                    const url = this.isEdit ? '/ItemConfig/UpdateWithScript' : '/ItemConfig/CreateWithScript';
                    console.log('请求URL:', url);

                    const response = await axios.post(url, saveData);
                    console.log('API响应:', response.data);

                    if (response.data.success) {
                        this.showMessage(response.data.message, 'success');
                        this.closeItemModal();
                        this.loadItems();
                    } else {
                        this.showMessage('保存失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    console.error('保存请求异常:', error);
                    this.showMessage('保存失败：' + error.message, 'error');
                } finally {
                    // 恢复保存状态
                    this.isSaving = false;

                    // 恢复按钮状态
                    if (saveBtn) saveBtn.disabled = false;
                    if (saveSpinner) saveSpinner.style.display = 'none';
                    if (saveText) saveText.textContent = '保存';
                    console.log('按钮状态已恢复');
                }
            },
            async saveScript() {
                if (!this.currentItem) {
                    this.showMessage('请先选择要编辑脚本的道具', 'warning');
                    return;
                }

                this.saving = true;
                try {
                    const response = await axios.post('/ItemConfig/UpdateScript', {
                        itemNo: this.currentItem.itemNo,
                        script: this.scriptForm.script,
                        description: this.scriptForm.description
                    });

                    if (response.data.success) {
                        this.showMessage(response.data.message, 'success');
                        this.closeScriptModal();
                        this.loadItems();
                    } else {
                        this.showMessage('保存脚本失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('保存脚本失败：' + error.message, 'error');
                } finally {
                    this.saving = false;
                }
            },
            async deleteItem(id) {
                if (!confirm('确定要删除这个道具吗？')) {
                    return;
                }

                try {
                    const response = await axios.post('/ItemConfig/Delete', { id });
                    if (response.data.success) {
                        this.showMessage(response.data.message, 'success');
                        this.loadItems();
                    } else {
                        this.showMessage('删除失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('删除失败：' + error.message, 'error');
                }
            },
            closeItemModal() {
                $('#itemModal').modal('hide');
            },
            closeDetailModal() {
                $('#detailModal').modal('hide');
            },
            closeScriptModal() {
                $('#scriptModal').modal('hide');
            },
            // 保留旧的closeModal方法以兼容现有代码
            closeModal() {
                $('.modal').modal('hide');
                this.saving = false;
                this.resetFormDataCompletely();
            },
            resetFormData() {

                // 重置道具表单数据
                this.itemForm = {
                    id: 0,
                    itemNo: null,
                    name: '',
                    type: '',
                    quality: '',
                    price: null,
                    icon: '',
                    useLimit: '',
                    description: '',
                    extra: '',
                    isActive: true
                };

                // 重置脚本表单数据
                this.scriptForm = {
                    id: null,
                    itemNo: null,
                    script: '',
                    description: ''
                };

                // 重置状态（但不重置isEdit，因为在编辑模式下调用时需要保持编辑状态）
                this.saving = false;

                // 同时清空DOM表单
                this.clearFormData();
            },
            resetFormDataCompletely() {
                // 完全重置表单数据，包括编辑状态
                this.resetFormData();
                this.isEdit = false;
                this.modalTitle = '';
            },


            formatDate(date) {
                if (!date) return '-';
                return new Date(date).toLocaleString();
            },
            showMessage(message, type) {
                // 创建提示元素
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'danger'} alert-dismissible fade show`;
                alertDiv.style.position = 'fixed';
                alertDiv.style.top = '20px';
                alertDiv.style.right = '20px';
                alertDiv.style.zIndex = '9999';
                alertDiv.style.minWidth = '300px';
                
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                
                document.body.appendChild(alertDiv);
                
                // 3秒后自动移除
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 3000);
            },
            getScriptPreview(script) {
                if (!script) return '';
                // 移除多余的空格和换行
                const cleanScript = script.trim();
                if (cleanScript.length <= 50) {
                    return cleanScript;
                }
                // 如果超过50个字符，截取前50个字符并添加省略号
                return cleanScript.substring(0, 50) + '...';
            },
            toggleSelectAll() {
                if (this.selectAll) {
                    this.selectedItems = this.items.map(item => item.id);
                } else {
                    this.selectedItems = [];
                }
            },
            async batchEnable() {
                if (this.selectedItems.length === 0) {
                    this.showMessage('请选择要启用的道具', 'warning');
                    return;
                }

                if (!confirm(`确定要启用选中的 ${this.selectedItems.length} 个道具吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/ItemConfig/BatchUpdateStatus', {
                        ids: this.selectedItems,
                        isActive: true
                    });

                    if (response.data.success) {
                        this.showMessage(response.data.message, 'success');
                        this.selectedItems = [];
                        this.selectAll = false;
                        this.loadItems();
                    } else {
                        this.showMessage('批量启用失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('批量启用失败：' + error.message, 'error');
                }
            },
            async batchDisable() {
                if (this.selectedItems.length === 0) {
                    this.showMessage('请选择要禁用的道具', 'warning');
                    return;
                }

                if (!confirm(`确定要禁用选中的 ${this.selectedItems.length} 个道具吗？`)) {
                    return;
                }

                try {
                    const response = await axios.post('/ItemConfig/BatchUpdateStatus', {
                        ids: this.selectedItems,
                        isActive: false
                    });

                    if (response.data.success) {
                        this.showMessage(response.data.message, 'success');
                        this.selectedItems = [];
                        this.selectAll = false;
                        this.loadItems();
                    } else {
                        this.showMessage('批量禁用失败：' + response.data.message, 'error');
                    }
                } catch (error) {
                    this.showMessage('批量禁用失败：' + error.message, 'error');
                }
            }
        }
    });

    const mountedApp = app.mount('#app');

    // 将Vue应用实例设为全局变量，以便onclick事件可以访问
    window.vueApp = mountedApp;
    });
</script>
