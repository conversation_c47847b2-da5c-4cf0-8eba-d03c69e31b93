﻿@{
    ViewData["Title"] = "控制中心";
}

<style>
    :root {
        --cyber-blue: #00d4ff;
        --cyber-purple: #8b5cf6;
        --cyber-pink: #ec4899;
        --cyber-green: #10b981;
        --cyber-orange: #f59e0b;
        --cyber-red: #ef4444;
        --dark-bg: #0a0a0f;
        --dark-card: #1a1a2e;
        --dark-surface: #16213e;
        --neon-glow: 0 0 20px rgba(0, 212, 255, 0.5);
        --card-glow: 0 8px 32px rgba(0, 0, 0, 0.3);
        --transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    body {
        background: linear-gradient(135deg, #0a0a0f 0%, #1a1a2e 50%, #16213e 100%);
        background-attachment: fixed;
        color: #e2e8f0;
        overflow-x: hidden;
    }

    /* 科幻粒子背景 */
    .cyber-bg {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background:
            radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
        animation: float 20s ease-in-out infinite;
    }

    .cyber-bg::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
            radial-gradient(circle at 1px 1px, rgba(0, 212, 255, 0.3) 1px, transparent 0);
        background-size: 50px 50px;
        animation: matrix 30s linear infinite;
        opacity: 0.1;
    }

    @@keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        33% { transform: translateY(-20px) rotate(1deg); }
        66% { transform: translateY(10px) rotate(-1deg); }
    }

    @@keyframes matrix {
        0% { transform: translateY(0); }
        100% { transform: translateY(-50px); }
    }

    /* 主控制台 */
    .command-center {
        background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 20px;
        padding: 40px;
        margin-bottom: 40px;
        position: relative;
        overflow: hidden;
        box-shadow: var(--card-glow), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }

    .command-center::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--cyber-blue), transparent);
        animation: scan 3s ease-in-out infinite;
    }

    @@keyframes scan {
        0%, 100% { opacity: 0; transform: translateX(-100%); }
        50% { opacity: 1; transform: translateX(100%); }
    }

    .command-title {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 20px;
        text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
        animation: glow 2s ease-in-out infinite alternate;
    }

    @@keyframes glow {
        from { filter: brightness(1); }
        to { filter: brightness(1.2); }
    }

    .status-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .status-item {
        background: rgba(0, 212, 255, 0.1);
        border: 1px solid rgba(0, 212, 255, 0.3);
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        backdrop-filter: blur(10px);
        transition: var(--transition);
    }

    .status-item:hover {
        border-color: var(--cyber-blue);
        box-shadow: var(--neon-glow);
        transform: translateY(-5px);
    }

    /* 模块卡片 */
    .module-card {
        background: linear-gradient(135deg, var(--dark-card) 0%, var(--dark-surface) 100%);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        transition: var(--transition);
        box-shadow: var(--card-glow);
    }

    .module-card:hover {
        border-color: var(--cyber-blue);
        box-shadow: var(--neon-glow), var(--card-glow);
        transform: translateY(-10px);
    }

    .module-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--module-gradient);
    }

    .module-card.system::before { background: linear-gradient(90deg, var(--cyber-blue), var(--cyber-purple)); }
    .module-card.game::before { background: linear-gradient(90deg, var(--cyber-green), var(--cyber-blue)); }
    .module-card.config::before { background: linear-gradient(90deg, var(--cyber-orange), var(--cyber-pink)); }
    .module-card.advanced::before { background: linear-gradient(90deg, var(--cyber-red), var(--cyber-purple)); }

    .module-header {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 25px;
    }

    .module-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: var(--module-gradient);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    }

    .module-card.system .module-icon { background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple)); }
    .module-card.game .module-icon { background: linear-gradient(135deg, var(--cyber-green), var(--cyber-blue)); }
    .module-card.config .module-icon { background: linear-gradient(135deg, var(--cyber-orange), var(--cyber-pink)); }
    .module-card.advanced .module-icon { background: linear-gradient(135deg, var(--cyber-red), var(--cyber-purple)); }

    .module-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #e2e8f0;
        margin: 0;
    }

    .module-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .module-item {
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 20px;
        transition: var(--transition);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .module-item:hover {
        background: rgba(0, 212, 255, 0.1);
        border-color: var(--cyber-blue);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
    }

    .module-item::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        transition: left 0.5s;
    }

    .module-item:hover::before {
        left: 100%;
    }

    .item-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: linear-gradient(135deg, var(--cyber-blue), var(--cyber-purple));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        margin-bottom: 15px;
    }

    .item-title {
        font-weight: 600;
        color: #e2e8f0;
        margin-bottom: 8px;
        font-size: 1rem;
    }

    .item-desc {
        color: #94a3b8;
        font-size: 0.85rem;
        line-height: 1.4;
        margin: 0;
    }

    /* 响应式设计 */
    @@media (max-width: 768px) {
        .command-center {
            padding: 25px;
            margin-bottom: 25px;
        }

        .command-title {
            font-size: 2rem;
        }

        .status-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }

        .module-card {
            padding: 20px;
            margin-bottom: 20px;
        }

        .module-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    }
</style>

<!-- 科幻背景 -->
<div class="cyber-bg"></div>

<div id="homeApp">
    <!-- 主控制台 -->
    <div class="command-center">
        <div class="text-center">
            <h1 class="command-title">
                <i class="fas fa-satellite-dish"></i> 口袋世界控制中心
            </h1>
            <p class="text-muted mb-4">欢迎回来，指挥官 {{ currentUser }}</p>

            <div class="status-grid">
                <div class="status-item">
                    <div><strong>系统状态</strong></div>
                    <div class="text-success">{{ systemStatus }}</div>
                </div>
                <div class="status-item">
                    <div><strong>在线用户</strong></div>
                    <div class="text-info">{{ todayActiveCount }}</div>
                </div>
                <div class="status-item">
                    <div><strong>系统时间</strong></div>
                    <div class="text-warning">{{ currentTime }}</div>
                </div>
                <div class="status-item">
                    <div><strong>总用户数</strong></div>
                    <div class="text-primary">{{ userCount }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能模块 -->
    <div class="row">
        <!-- 系统管理模块 -->
        <div class="col-lg-6 col-xl-3 mb-4">
            <div class="module-card system">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h5 class="module-title">系统管理</h5>
                </div>
                <div class="module-grid">
                    <div class="module-item" v-on:click="goToAdminManage">
                        <div class="item-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div class="item-title">管理员管理</div>
                        <p class="item-desc">管理系统管理员账户和权限</p>
                    </div>
                    <div class="module-item" v-on:click="showDevelopingAlert">
                        <div class="item-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="item-title">权限管理</div>
                        <p class="item-desc">配置用户角色和访问权限</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户管理模块 -->
        <div class="col-lg-6 col-xl-3 mb-4">
            <div class="module-card game">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h5 class="module-title">用户管理</h5>
                </div>
                <div class="module-grid">
                    <div class="module-item" v-on:click="goToUserManage">
                        <div class="item-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="item-title">用户管理</div>
                        <p class="item-desc">管理游戏用户账户信息</p>
                    </div>
                    <div class="module-item" v-on:click="goToUserPetManage">
                        <div class="item-icon">
                            <i class="fas fa-paw"></i>
                        </div>
                        <div class="item-title">用户宠物</div>
                        <p class="item-desc">管理用户的宠物数据</p>
                    </div>
                    <div class="module-item" v-on:click="goToUserItemManage">
                        <div class="item-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div class="item-title">用户道具</div>
                        <p class="item-desc">管理用户的道具背包</p>
                    </div>
                    <div class="module-item" v-on:click="goToUserEquipmentManage">
                        <div class="item-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="item-title">用户装备</div>
                        <p class="item-desc">管理用户的装备数据</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 游戏配置模块 -->
        <div class="col-lg-6 col-xl-3 mb-4">
            <div class="module-card config">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h5 class="module-title">游戏配置</h5>
                </div>
                <div class="module-grid">
                    <div class="module-item" v-on:click="goToMapConfig">
                        <div class="item-icon">
                            <i class="fas fa-map"></i>
                        </div>
                        <div class="item-title">地图配置</div>
                        <p class="item-desc">配置游戏地图和场景</p>
                    </div>
                    <div class="module-item" v-on:click="goToPetConfig">
                        <div class="item-icon">
                            <i class="fas fa-dog"></i>
                        </div>
                        <div class="item-title">宠物配置</div>
                        <p class="item-desc">配置宠物属性和技能</p>
                    </div>
                    <div class="module-item" v-on:click="goToMonsterConfig">
                        <div class="item-icon">
                            <i class="fas fa-spider"></i>
                        </div>
                        <div class="item-title">怪物配置</div>
                        <p class="item-desc">配置怪物属性和AI</p>
                    </div>
                    <div class="module-item" v-on:click="goToItemConfig">
                        <div class="item-icon">
                            <i class="fas fa-cubes"></i>
                        </div>
                        <div class="item-title">道具配置</div>
                        <p class="item-desc">配置游戏道具和物品</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 高级功能模块 -->
        <div class="col-lg-6 col-xl-3 mb-4">
            <div class="module-card advanced">
                <div class="module-header">
                    <div class="module-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h5 class="module-title">高级功能</h5>
                </div>
                <div class="module-grid">
                    <div class="module-item" v-on:click="goToPetEvolutionConfig">
                        <div class="item-icon">
                            <i class="fas fa-dna"></i>
                        </div>
                        <div class="item-title">宠物进化</div>
                        <p class="item-desc">配置宠物进化规则</p>
                    </div>
                    <div class="module-item" v-on:click="goToPetSynthesisFormula">
                        <div class="item-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <div class="item-title">宠物合成</div>
                        <p class="item-desc">配置宠物合成公式</p>
                    </div>
                    <div class="module-item" v-on:click="goToDropConfig">
                        <div class="item-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="item-title">掉落配置</div>
                        <p class="item-desc">配置物品掉落规则</p>
                    </div>
                    <div class="module-item" v-on:click="goToTaskConfig">
                        <div class="item-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="item-title">任务配置</div>
                        <p class="item-desc">配置游戏任务系统</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // 初始化Vue应用
        Vue.createApp({
            data() {
                return {
                    currentUser: '@User.Identity.Name',
                    currentTime: '',
                    userCount: '...',
                    adminCount: '...',
                    todayActiveCount: '...',
                    systemStatus: '运行正常'
                };
            },
            methods: {
                // 更新当前时间
                updateTime() {
                    this.currentTime = new Date().toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });
                },

                // 系统管理
                goToAdminManage() {
                    window.location.href = '@Url.Action("Index", "AdminBm")';
                },

                // 用户管理
                goToUserManage() {
                    window.location.href = '@Url.Action("Index", "User")';
                },

                goToUserPetManage() {
                    window.location.href = '@Url.Action("Index", "UserPet")';
                },

                goToUserItemManage() {
                    window.location.href = '@Url.Action("Index", "UserItem")';
                },

                goToUserEquipmentManage() {
                    window.location.href = '@Url.Action("Index", "UserEquipment")';
                },

                // 游戏配置
                goToMapConfig() {
                    window.location.href = '@Url.Action("Index", "MapConfig")';
                },

                goToPetConfig() {
                    window.location.href = '@Url.Action("Index", "PetConfig")';
                },

                goToMonsterConfig() {
                    window.location.href = '@Url.Action("Index", "MonsterConfig")';
                },

                goToItemConfig() {
                    window.location.href = '@Url.Action("Index", "ItemConfig")';
                },

                // 高级功能
                goToPetEvolutionConfig() {
                    window.location.href = '@Url.Action("Index", "PetEvolutionConfig")';
                },

                goToPetSynthesisFormula() {
                    window.location.href = '@Url.Action("Index", "PetSynthesisFormula")';
                },

                goToDropConfig() {
                    window.location.href = '@Url.Action("Index", "DropConfig")';
                },

                goToTaskConfig() {
                    window.location.href = '@Url.Action("Index", "TaskConfig")';
                },

                // 显示开发中提示
                showDevelopingAlert() {
                    if (typeof toastr !== 'undefined') {
                        toastr.info('功能开发中，敬请期待！', '提示');
                    } else {
                        alert('功能开发中...');
                    }
                },

                // 加载统计数据
                loadStats() {
                    // 模拟加载延迟，增加真实感
                    setTimeout(() => {
                        this.userCount = '1,234';
                        this.adminCount = '5';
                        this.todayActiveCount = '89';
                    }, 800);
                }
            },

            mounted() {
                // 初始化时间并每秒更新
                this.updateTime();
                setInterval(this.updateTime, 1000);

                // 加载统计数据
                this.loadStats();

                // 添加入场动画
                const cards = document.querySelectorAll('.module-card');
                cards.forEach((card, index) => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(30px)';
                    setTimeout(() => {
                        card.style.transition = 'all 0.6s ease-out';
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, index * 150);
                });
            }
        }).mount('#homeApp');
    </script>
}
