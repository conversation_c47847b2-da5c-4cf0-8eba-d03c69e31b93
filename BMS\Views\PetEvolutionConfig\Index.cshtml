@{
    ViewData["Title"] = "宠物进化配置管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- 科幻背景 -->
<div class="cyber-bg"></div>




        transition: var(--transition);
        margin-bottom: 2rem;
    }

    .modern-card:hover {
        box-shadow: var(--card-hover-shadow);
        transform: translateY(-2px);
    }

    .modern-card-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        padding: 1.5rem;
    }

    .modern-card-title {
        font-weight: 600;
        font-size: 1.25rem;
        color: #1e293b;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .modern-card-title i {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-size: 1.1em;
    }

    .modern-btn {
        border-radius: 12px;
        font-weight: 500;
        padding: 0.75rem 1.5rem;
        transition: var(--transition);
        border: none;
        position: relative;
        overflow: hidden;
    }

    .modern-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .modern-btn:hover::before {
        left: 100%;
    }

    .modern-btn-primary {
        background: var(--primary-gradient);
        color: white;
    }

    .modern-btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-success {
        background: var(--success-gradient);
        color: #f8f9fa;
    }

    .modern-btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(17, 153, 142, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-warning {
        background: var(--warning-gradient);
        color: #f8f9fa;
    }

    .modern-btn-warning:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(240, 147, 251, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-info {
        background: var(--info-gradient);
        color: #f8f9fa;
    }

    .modern-btn-info:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(79, 172, 254, 0.3);
        color: #f8f9fa;
    }

    .modern-btn-danger {
        background: var(--danger-gradient);
        color: #f8f9fa;
    }

    .modern-btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(250, 112, 154, 0.3);
        color: #f8f9fa;
    }

    .modern-form-control {
        border-radius: 10px;
        border: 2px solid #e2e8f0;
        padding: 0.75rem 1rem;
        transition: var(--transition);
        background: rgba(255, 255, 255, 0.9);
    }

    .modern-form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        background: white;
    }

    .modern-table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        background: white;
    }

    .modern-table thead th {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border: none;
        font-weight: 600;
        color: #475569;
        padding: 1rem;
    }

    .modern-table tbody tr {
        transition: var(--transition);
    }

    .modern-table tbody tr:hover {
        background: rgba(102, 126, 234, 0.05);
        transform: scale(1.001);
    }

    .modern-table tbody td {
        padding: 1rem;
        border-color: #f1f5f9;
    }

    .modern-pagination {
        gap: 0.5rem;
    }

    .modern-pagination .page-link {
        border-radius: 10px;
        border: none;
        margin: 0 2px;
        padding: 0.6rem 1rem;
        transition: var(--transition);
    }

    .modern-pagination .page-item.active .page-link {
        background: var(--primary-gradient);
        border: none;
    }

    .modern-pagination .page-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .modern-badge {
        border-radius: 8px;
        padding: 0.4rem 0.8rem;
        font-weight: 500;
        font-size: 0.75rem;
    }

    .modern-breadcrumb {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        margin-bottom: 0;
    }

    .fade-in {
        animation: fadeInUp 0.6s ease forwards;
    }

    .fade-in-delay-1 {
        animation: fadeInUp 0.6s ease 0.1s forwards;
        opacity: 0;
    }

    .fade-in-delay-2 {
        animation: fadeInUp 0.6s ease 0.2s forwards;
        opacity: 0;
    }

    @@keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-content {
        border-radius: var(--border-radius);
        border: none;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    }

    .modal-header {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .content-header h1 {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        font-weight: 700;
    }

    /* 自定义搜索下拉框样式 */
    .custom-select-container {
        position: relative;
        width: 100%;
    }

    .custom-select-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e2e8f0;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.9);
        transition: var(--transition);
        cursor: pointer;
    }

    .custom-select-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        outline: none;
    }

    .custom-select-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .custom-select-dropdown.show {
        display: block;
    }

    .custom-select-search {
        padding: 0.75rem;
        border: none;
        border-bottom: 1px solid #e2e8f0;
        width: 100%;
        border-radius: 8px 8px 0 0;
    }

    .custom-select-search:focus {
        outline: none;
        border-color: #667eea;
    }

    .custom-select-option {
        padding: 0.75rem;
        cursor: pointer;
        border-bottom: 1px solid #f1f5f9;
        transition: var(--transition);
    }

    .custom-select-option:hover {
        background: rgba(102, 126, 234, 0.1);
    }

    .custom-select-option.selected {
        background: var(--primary-gradient);
        color: white;
    }

    .custom-select-option .item-name {
        font-weight: 600;
        color: #1e293b;
    }

    .custom-select-option .item-details {
        font-size: 0.875rem;
        color: #64748b;
        margin-top: 0.25rem;
    }

    .custom-select-option.selected .item-name,
    .custom-select-option.selected .item-details {
        color: white;
    }

    .custom-select-arrow {
        position: absolute;
        right: 1rem;
        top: 50%;
        transform: translateY(-50%);
        transition: var(--transition);
    }

    .custom-select-container.open .custom-select-arrow {
        transform: translateY(-50%) rotate(180deg);
    }
</style>

<div id="petEvolutionConfigApp">
    <!-- 主要内容 -->
    <section class="content">
        <div class="container-fluid">
            <div id="petEvolutionConfigApp">
                <!-- 页面标题 -->
                <div class="content-header">
                    <div class="container-fluid">
                        <div class="row mb-2">
                            <div class="col-sm-6">
                                <h1 class="m-0 fade-in">
                                    <i class="fas fa-dna mr-2"></i>宠物进化配置管理
                                </h1>
                            </div>
                            <div class="col-sm-6">
                                <ol class="modern-breadcrumb breadcrumb float-sm-right fade-in-delay-1">
                                    <li class="breadcrumb-item"><a href="/Home" style="color: #667eea; text-decoration: none;">首页</a></li>
                                    <li class="breadcrumb-item active">宠物进化配置管理</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索条件 -->
                <div class="modern-card fade-in-delay-1">
                    <div class="modern-card-header">
                        <h3 class="modern-card-title">
                            <i class="fas fa-search"></i>搜索条件
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>宠物编号</label>
                                    <input type="number" v-model.number="queryForm.petNo" class="modern-form-control" placeholder="请输入宠物编号">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>宠物名称</label>
                                    <input type="text" v-model="queryForm.petName" class="modern-form-control" placeholder="请输入宠物名称">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>进化类型</label>
                                    <select v-model="queryForm.evolutionType" class="modern-form-control">
                                        <option value="">全部类型</option>
                                        <option value="A">A路线</option>
                                        <option value="B">B路线</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>目标宠物编号</label>
                                    <input type="number" v-model.number="queryForm.targetPetNo" class="modern-form-control" placeholder="请输入目标宠物编号">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>目标宠物名称</label>
                                    <input type="text" v-model="queryForm.targetPetName" class="modern-form-control" placeholder="请输入目标宠物名称">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>激活状态</label>
                                    <select v-model="queryForm.isActive" class="modern-form-control">
                                        <option value="">全部状态</option>
                                        <option v-bind:value="true">已激活</option>
                                        <option v-bind:value="false">已禁用</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>最小等级要求</label>
                                    <input type="number" v-model.number="queryForm.minRequiredLevel" class="modern-form-control" placeholder="最小等级">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>最大等级要求</label>
                                    <input type="number" v-model.number="queryForm.maxRequiredLevel" class="modern-form-control" placeholder="最大等级">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div class="d-block">
                                        <button type="button" class="modern-btn modern-btn-primary" v-on:click="searchConfigs" v-bind:disabled="loading">
                                            <i class="fas fa-search"></i> 搜索
                                        </button>
                                        <button type="button" class="modern-btn btn-secondary ml-2" v-on:click="resetQuery">
                                            <i class="fas fa-redo"></i> 重置
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-success ml-2" v-on:click="showAddModal">
                                            <i class="fas fa-plus"></i> 新增配置
                                        </button>
                                        <button type="button" class="modern-btn modern-btn-danger ml-2" v-on:click="batchDelete" v-bind:disabled="selectedIds.length === 0">
                                            <i class="fas fa-trash"></i> 批量删除
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据列表 -->
                <div class="modern-card fade-in-delay-2">
                    <div class="modern-card-header">
                        <h3 class="modern-card-title">
                            <i class="fas fa-list"></i>进化配置列表
                        </h3>
                    </div>
                    <div class="card-body table-responsive p-0">
                        <table class="modern-table table table-hover text-nowrap">
                            <thead>
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" v-model="selectAll" v-on:change="toggleSelectAll">
                                    </th>
                                    <th>ID</th>
                                    <th>宠物信息</th>
                                    <th>进化类型</th>
                                    <th>目标宠物</th>
                                    <th>等级要求</th>
                                    <th>所需道具</th>
                                    <th>消耗金币</th>
                                    <th>成长加成</th>
                                    <th>成功率</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-if="loading">
                                    <td colspan="13" class="text-center">
                                        <i class="fas fa-spinner fa-spin"></i> 加载中...
                                    </td>
                                </tr>
                                <tr v-else-if="configsList.length === 0">
                                    <td colspan="13" class="text-center">暂无数据</td>
                                </tr>
                                <tr v-else v-for="config in configsList" v-bind:key="config.id">
                                    <td>
                                        <input type="checkbox" v-model="selectedIds" v-bind:value="config.id">
                                    </td>
                                    <td>{{ config.id }}</td>
                                    <td>
                                        <div>
                                            <strong>{{ config.petName }}</strong> ({{ config.petAttribute }})
                                            <br>
                                            <small class="text-muted">编号: {{ config.petNo }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="modern-badge" v-bind:class="config.evolutionType === 'A' ? 'badge-primary' : 'badge-info'">
                                            {{ config.evolutionType }}路线
                                        </span>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ config.targetPetName }}</strong> ({{ config.targetPetAttribute }})
                                            <br>
                                            <small class="text-muted">编号: {{ config.targetPetNo }}</small>
                                        </div>
                                    </td>
                                    <td>{{ config.requiredLevel }}级</td>
                                    <td>
                                        <div v-if="config.requiredItemId">
                                            {{ config.requiredItemName || config.requiredItemId }}
                                            <br>
                                            <small class="text-muted">数量: {{ config.requiredItemCount }}</small>
                                        </div>
                                        <span v-else class="text-muted">无</span>
                                    </td>
                                    <td>{{ config.costGold.toLocaleString() }}</td>
                                    <td>{{ config.growthMin }} - {{ config.growthMax }}</td>
                                    <td>{{ config.successRate }}%</td>
                                    <td>
                                        <span class="modern-badge" v-bind:class="config.isActive ? 'badge-success' : 'badge-secondary'">
                                            {{ config.isActive ? '已激活' : '已禁用' }}
                                        </span>
                                    </td>
                                    <td>{{ formatDateTime(config.createTime) }}</td>
                                    <td>
                                        <button type="button" class="btn btn-info btn-sm" v-on:click="viewDetail(config)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button type="button" class="btn btn-warning btn-sm ml-1" v-on:click="editConfig(config)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm ml-1" v-bind:class="config.isActive ? 'btn-secondary' : 'btn-success'" v-on:click="toggleActive(config)">
                                            <i v-bind:class="config.isActive ? 'fas fa-pause' : 'fas fa-play'"></i>
                                        </button>
                                        <button type="button" class="btn btn-danger btn-sm ml-1" v-on:click="deleteConfig(config)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="card-footer" v-if="totalCount > 0">
                        <div class="row align-items-center">
                            <div class="col-sm-12 col-md-5">
                                <div class="dataTables_info">
                                    显示第 {{ (currentPage - 1) * pageSize + 1 }} 到 {{ Math.min(currentPage * pageSize, totalCount) }} 条，共 {{ totalCount }} 条记录
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-7">
                                <div class="dataTables_paginate paging_simple_numbers float-right">
                                    <ul class="modern-pagination pagination">
                                        <li class="paginate_button page-item previous" v-bind:class="{ disabled: currentPage === 1 }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage - 1)">上一页</a>
                                        </li>
                                        <li class="paginate_button page-item" 
                                            v-for="page in visiblePages" 
                                            v-bind:key="page" 
                                            v-bind:class="{ active: page === currentPage }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(page)">{{ page }}</a>
                                        </li>
                                        <li class="paginate_button page-item next" v-bind:class="{ disabled: currentPage === totalPages }">
                                            <a href="#" class="page-link" v-on:click.prevent="changePage(currentPage + 1)">下一页</a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 新增/编辑进化配置模态框 -->
<div class="modal fade modal-modern" id="configModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog"></i> {{ modalTitle }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">宠物 <span class="text-danger">*</span></label>
                            <select v-model="configForm.petNo" class="form-control form-control-modern" id="modalPetSelect">
                                <option value="">请选择宠物</option>
                                <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                    {{ pet.label }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">进化类型 <span class="text-danger">*</span></label>
                            <select v-model="configForm.evolutionType" class="form-control form-control-modern">
                                <option value="">请选择进化类型</option>
                                <option value="A">A路线</option>
                                <option value="B">B路线</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">目标宠物 <span class="text-danger">*</span></label>
                            <select v-model="configForm.targetPetNo" class="form-control form-control-modern" id="modalTargetPetSelect">
                                <option value="">请选择目标宠物</option>
                                <option v-for="pet in petOptions" v-bind:key="pet.value" v-bind:value="pet.value">
                                    {{ pet.label }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">所需等级</label>
                            <input type="number" v-model="configForm.requiredLevel" class="form-control form-control-modern"
                                   min="1" max="999" placeholder="40">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">所需道具</label>
                            <select v-model="configForm.requiredItemId" class="form-control form-control-modern" id="modalItemSelect">
                                <option value="">无需道具</option>
                                <option v-for="item in itemOptions" v-bind:key="item.value" v-bind:value="item.value">
                                    {{ item.label }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">道具数量</label>
                            <input type="number" v-model="configForm.requiredItemCount" class="form-control form-control-modern"
                                   min="0" placeholder="1">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">消耗金币</label>
                            <input type="number" v-model="configForm.costGold" class="form-control form-control-modern"
                                   min="0" placeholder="1000">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">成功率(%)</label>
                            <input type="number" v-model="configForm.successRate" class="form-control form-control-modern"
                                   min="0" max="100" step="0.01" placeholder="100.00">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">最小成长加成</label>
                            <input type="number" v-model="configForm.growthMin" class="form-control form-control-modern"
                                   min="0" max="9.999" step="0.001" placeholder="0.100">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">最大成长加成</label>
                            <input type="number" v-model="configForm.growthMax" class="form-control form-control-modern"
                                   min="0" max="9.999" step="0.001" placeholder="0.500">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">说明</label>
                            <input type="text" v-model="configForm.description" class="form-control form-control-modern"
                                   maxlength="50" placeholder="请输入说明">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <div class="form-check">
                                <input type="checkbox" v-model="configForm.isActive" class="form-check-input" id="isActiveCheck">
                                <label class="form-check-label" for="isActiveCheck">
                                    启用此进化配置
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-modern" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 取消
                </button>
                <button type="button" v-on:click="saveConfig" class="btn btn-primary-modern btn-modern">
                    <i class="fas fa-save"></i> 保存
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 详情模态框 -->
<div class="modal fade modal-modern" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle"></i> 进化配置详情
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div v-if="selectedConfig" class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">宠物信息</label>
                        <div class="p-3 bg-light rounded">
                            <div><strong>名称:</strong> {{ selectedConfig.petName }}</div>
                            <div><strong>编号:</strong> {{ selectedConfig.petNo }}</div>
                            <div><strong>属性:</strong> {{ selectedConfig.petAttribute }}</div>
                            <div><strong>进化类型:</strong> {{ selectedConfig.evolutionType }}路线</div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">目标宠物信息</label>
                        <div class="p-3 bg-light rounded">
                            <div><strong>名称:</strong> {{ selectedConfig.targetPetName }}</div>
                            <div><strong>编号:</strong> {{ selectedConfig.targetPetNo }}</div>
                            <div><strong>属性:</strong> {{ selectedConfig.targetPetAttribute }}</div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">进化条件</label>
                        <div class="p-3 bg-warning bg-opacity-10 rounded">
                            <div><strong>所需等级:</strong> {{ selectedConfig.requiredLevel }}级</div>
                            <div><strong>所需道具:</strong> {{ selectedConfig.requiredItemName || selectedConfig.requiredItemId || '无' }}</div>
                            <div><strong>道具数量:</strong> {{ selectedConfig.requiredItemCount }}</div>
                            <div><strong>消耗金币:</strong> {{ formatNumber(selectedConfig.costGold) }}</div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">进化效果</label>
                        <div class="p-3 bg-success bg-opacity-10 rounded">
                            <div><strong>成功率:</strong> {{ selectedConfig.successRate }}%</div>
                            <div><strong>最小成长加成:</strong> {{ selectedConfig.growthMin }}</div>
                            <div><strong>最大成长加成:</strong> {{ selectedConfig.growthMax }}</div>
                        </div>
                    </div>
                    <div class="col-md-12 mb-3">
                        <label class="form-label">配置信息</label>
                        <div class="p-3 bg-info bg-opacity-10 rounded">
                            <div><strong>配置ID:</strong> {{ selectedConfig.id }}</div>
                            <div><strong>激活状态:</strong>
                                <span v-bind:class="selectedConfig.isActive ? 'text-success' : 'text-danger'">
                                    {{ selectedConfig.isActive ? '已激活' : '已禁用' }}
                                </span>
                            </div>
                            <div><strong>说明:</strong> {{ selectedConfig.description || '无' }}</div>
                            <div><strong>创建时间:</strong> {{ formatDateTime(selectedConfig.createTime) }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-modern" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 关闭
                </button>
            </div>
        </div>
    </div>
</div>

            </div>
        </div>
    </section>
</div>

<script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
    new Vue({
        el: '#petEvolutionConfigApp',
        data: {
            // 加载状态
            loading: false,
            saving: false,

            // 列表数据
            configsList: [],
            totalCount: 0,
            currentPage: 1,
            pageSize: 10,

            // 查询条件
            queryForm: {
                petNo: null,
                petName: '',
                evolutionType: '',
                targetPetNo: null,
                targetPetName: '',
                isActive: null,
                minRequiredLevel: null,
                maxRequiredLevel: null
            },

            // 表单数据
            configForm: {
                id: 0,
                petNo: null,
                evolutionType: '',
                targetPetNo: null,
                requiredLevel: 40,
                requiredItemId: null,
                requiredItemCount: 1,
                costGold: 1000,
                growthMin: 0.100,
                growthMax: 0.500,
                successRate: 100.00,
                isActive: true,
                description: ''
            },

            // 模态框状态
            isEdit: false,
            modalTitle: '新增进化配置',
            selectedConfig: null,

            // 选择状态
            selectedIds: [],
            selectAll: false,

            // 下拉选项
            petOptions: [],
            itemOptions: []
        },
        computed: {
            // 总页数
            totalPages() {
                return Math.ceil(this.totalCount / this.pageSize);
            },

            // 可见页码
            visiblePages() {
                const current = this.currentPage;
                const total = this.totalPages;
                const delta = 2;

                let start = Math.max(1, current - delta);
                let end = Math.min(total, current + delta);

                if (end - start < 2 * delta) {
                    if (start === 1) {
                        end = Math.min(total, start + 2 * delta);
                    } else if (end === total) {
                        start = Math.max(1, end - 2 * delta);
                    }
                }

                const pages = [];
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }
                return pages;
            },


        },
        methods: {
            // 加载进化配置列表
            async loadConfigs() {
                this.loading = true;
                try {
                    const response = await axios.post('/PetEvolutionConfig/GetList', {
                        ...this.queryForm,
                        page: this.currentPage,
                        pageSize: this.pageSize
                    });

                    if (response.data.code === 200) {
                        this.configsList = response.data.data;
                        this.totalCount = response.data.total;
                    } else {
                        Swal.fire('错误', response.data.message || '获取数据失败', 'error');
                    }
                } catch (error) {
                    console.error('加载进化配置列表失败:', error);
                    Swal.fire('错误', '加载数据失败', 'error');
                } finally {
                    this.loading = false;
                }
            },

            // 加载宠物配置选项
            async loadPetOptions() {
                try {
                    const response = await axios.get('/PetEvolutionConfig/GetPetConfigOptions');
                    if (response.data.code === 200) {
                        this.petOptions = response.data.data;
                    }
                } catch (error) {
                    console.error('加载宠物配置选项失败:', error);
                }
            },

            // 加载道具配置选项
            async loadItemOptions() {
                try {
                    const response = await axios.get('/PetEvolutionConfig/GetItemConfigOptions');
                    if (response.data.code === 200) {
                        this.itemOptions = response.data.data;
                    }
                } catch (error) {
                    console.error('加载道具配置选项失败:', error);
                }
            },

            // 搜索配置
            searchConfigs() {
                this.currentPage = 1;
                this.loadConfigs();
            },

            // 重置查询条件
            resetQuery() {
                this.queryForm = {
                    petNo: null,
                    petName: '',
                    evolutionType: '',
                    targetPetNo: null,
                    targetPetName: '',
                    isActive: null,
                    minRequiredLevel: null,
                    maxRequiredLevel: null
                };
                this.searchConfigs();
            },

            // 切换页码
            changePage(page) {
                if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
                    this.currentPage = page;
                    this.loadConfigs();
                }
            },

            // 显示新增模态框
            showAddModal() {
                this.isEdit = false;
                this.modalTitle = '新增进化配置';
                this.configForm = {
                    id: 0,
                    petNo: null,
                    evolutionType: '',
                    targetPetNo: null,
                    requiredLevel: 40,
                    requiredItemId: null,
                    requiredItemCount: 1,
                    costGold: 1000,
                    growthMin: 0.100,
                    growthMax: 0.500,
                    successRate: 100.00,
                    isActive: true,
                    description: ''
                };

                new bootstrap.Modal(document.getElementById('configModal')).show();
            },

            // 编辑配置
            editConfig(config) {
                this.isEdit = true;
                this.modalTitle = '编辑进化配置';
                this.configForm = {
                    id: config.id,
                    petNo: config.petNo,
                    evolutionType: config.evolutionType,
                    targetPetNo: config.targetPetNo,
                    requiredLevel: config.requiredLevel,
                    requiredItemId: config.requiredItemId,
                    requiredItemCount: config.requiredItemCount,
                    costGold: config.costGold,
                    growthMin: config.growthMin,
                    growthMax: config.growthMax,
                    successRate: config.successRate,
                    isActive: config.isActive,
                    description: config.description
                };

                new bootstrap.Modal(document.getElementById('configModal')).show();
            },

            // 保存配置
            async saveConfig() {
                // 验证表单
                if (!this.configForm.petNo) {
                    Swal.fire('错误', '请选择宠物', 'error');
                    return;
                }
                if (!this.configForm.evolutionType) {
                    Swal.fire('错误', '请选择进化类型', 'error');
                    return;
                }
                if (!this.configForm.targetPetNo) {
                    Swal.fire('错误', '请选择目标宠物', 'error');
                    return;
                }
                if (!this.configForm.requiredLevel || this.configForm.requiredLevel < 1) {
                    Swal.fire('错误', '请输入有效的所需等级', 'error');
                    return;
                }
                if (this.configForm.growthMin > this.configForm.growthMax) {
                    Swal.fire('错误', '最小成长加成不能大于最大成长加成', 'error');
                    return;
                }

                this.saving = true;
                try {
                    const url = this.isEdit ? '/PetEvolutionConfig/Update' : '/PetEvolutionConfig/Create';
                    const response = await axios.post(url, this.configForm);

                    if (response.data.code === 200) {
                        Swal.fire('成功', response.data.message || '操作成功', 'success');
                        bootstrap.Modal.getInstance(document.getElementById('configModal')).hide();
                        this.loadConfigs();
                    } else {
                        Swal.fire('错误', response.data.message || '操作失败', 'error');
                    }
                } catch (error) {
                    console.error('保存配置失败:', error);
                    Swal.fire('错误', '保存失败', 'error');
                } finally {
                    this.saving = false;
                }
            },

            // 删除配置
            async deleteConfig(config) {
                const result = await Swal.fire({
                    title: '确认删除',
                    text: `确定要删除宠物${config.petName}的${config.evolutionType}路线进化配置吗？`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                });

                if (result.isConfirmed) {
                    try {
                        const response = await axios.post('/PetEvolutionConfig/Delete', { id: config.id });
                        if (response.data.code === 200) {
                            Swal.fire('成功', '删除成功', 'success');
                            this.loadConfigs();
                        } else {
                            Swal.fire('错误', response.data.message || '删除失败', 'error');
                        }
                    } catch (error) {
                        console.error('删除配置失败:', error);
                        Swal.fire('错误', '删除失败', 'error');
                    }
                }
            },

            // 批量删除
            async batchDelete() {
                if (this.selectedIds.length === 0) {
                    Swal.fire('提示', '请选择要删除的配置', 'info');
                    return;
                }

                const result = await Swal.fire({
                    title: '确认批量删除',
                    text: `确定要删除选中的${this.selectedIds.length}个进化配置吗？`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: '确定',
                    cancelButtonText: '取消'
                });

                if (result.isConfirmed) {
                    try {
                        const response = await axios.post('/PetEvolutionConfig/BatchDelete', this.selectedIds);
                        if (response.data.code === 200) {
                            Swal.fire('成功', response.data.message || '批量删除成功', 'success');
                            this.selectedIds = [];
                            this.selectAll = false;
                            this.loadConfigs();
                        } else {
                            Swal.fire('错误', response.data.message || '批量删除失败', 'error');
                        }
                    } catch (error) {
                        console.error('批量删除失败:', error);
                        Swal.fire('错误', '批量删除失败', 'error');
                    }
                }
            },

            // 切换激活状态
            async toggleActive(config) {
                try {
                    const response = await axios.post('/PetEvolutionConfig/ToggleActive', {
                        id: config.id,
                        isActive: !config.isActive
                    });

                    if (response.data.code === 200) {
                        Swal.fire('成功', response.data.message || '操作成功', 'success');
                        this.loadConfigs();
                    } else {
                        Swal.fire('错误', response.data.message || '操作失败', 'error');
                    }
                } catch (error) {
                    console.error('切换激活状态失败:', error);
                    Swal.fire('错误', '操作失败', 'error');
                }
            },

            // 查看详情
            viewDetail(config) {
                this.selectedConfig = config;
                new bootstrap.Modal(document.getElementById('detailModal')).show();
            },



            // 全选/取消全选
            toggleSelectAll() {
                if (this.selectAll) {
                    this.selectedIds = this.configsList.map(config => config.id);
                } else {
                    this.selectedIds = [];
                }
            },

            // 格式化日期时间
            formatDateTime(dateTime) {
                if (!dateTime) return '';
                const date = new Date(dateTime);
                return date.toLocaleString('zh-CN');
            },

            // 格式化数字
            formatNumber(number) {
                if (!number) return '0';
                return number.toLocaleString();
            }
        },
        watch: {
            // 监听选中项变化
            selectedIds() {
                this.selectAll = this.selectedIds.length === this.configsList.length && this.configsList.length > 0;
            }
        },
        async mounted() {
            // 初始化加载
            await this.loadPetOptions();
            await this.loadItemOptions();
            await this.loadConfigs();
        }
    });
</script>
